// 清理后端的resources中的前端文件
tasks.register('clean_resources_agent', Delete) {

    delete rootDir.getAbsolutePath() + '/torch-yun-backend/torch-yun-main/src/main/resources/agent'
}

// 把插件拷贝到resource中
tasks.register('coty_resources_agent', Copy) {

    mustRunAfter(":torch-yun-dist:build_agent", "clean_resources_agent")

    from rootDir.getAbsolutePath() + '/torch-yun-dist/build/distributions/zhishuyun-agent.tar.gz'
    into rootDir.getAbsolutePath() + '/torch-yun-backend/torch-yun-main/src/main/resources/agent'
}

// 后端打包
tasks.register('make', GradleBuild) {

    mustRunAfter(":torch-yun-dist:build_agent")
    dependsOn("clean_resources_agent", "coty_resources_agent")

    // 构建后端
    tasks = [":torch-yun-backend:torch-yun-main:bootJar"]
}