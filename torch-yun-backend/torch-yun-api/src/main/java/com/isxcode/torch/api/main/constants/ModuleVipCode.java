package com.isxcode.torch.api.main.constants;

/**
 * 商业模块编码
 */
public interface ModuleVipCode {

    /**
     * 许可证模块
     */
    String VIP_LICENSE = "/vip/license";

    /**
     * 作业调度模块
     */
    String VIP_WORK = "/vip/work";

    /**
     * 作业实例模块
     */
    String VIP_WORK_INSTANCE = "/vip/work-instance";

    /**
     * 作业流实例模块
     */
    String VIP_WORKFLOW_INSTANCE = "/vip/workflow-instance";

    /**
     * 接口服务模块
     */
    String VIP_API = "/vip/api-service";

    /**
     * 分享表单
     */
    String VIP_FORM = "/vip/form";

    /**
     * 容器模块
     */
    String VIP_CONTAINER = "/vip/container";

    /**
     * 实时计算模块
     */
    String VIP_REAL = "/vip/real";

    /**
     * 数据大屏模块
     */
    String VIP_VIEW = "/vip/view";

    /**
     * 元数据模块
     */
    String VIP_META = "/vip/meta";
}
