package com.isxcode.torch.api.main.constants;

/**
 * 模块编码
 */
public interface ModuleCode {

    /**
     * 用户模块
     */
    String USER = "user";

    /**
     * 计算集群模块
     */
    String CLUSTER = "cluster";

    /**
     * 集群节点模块
     */
    String CLUSTER_NODE = "cluster-node";

    /**
     * 数据源模块
     */
    String DATASOURCE = "datasource";

    /**
     * 作业流模块
     */
    String WORKFLOW = "workflow";

    /**
     * 作业模块
     */
    String WORK = "work";

    /**
     * 租户模块
     */
    String TENANT = "tenant";

    /**
     * 租户用户模块
     */
    String TENANT_USER = "tenant-user";

    /**
     * 至慧云代理模块
     */
    String PYTORCH_YUN_AGENT = "torch-yun-agent";

    /**
     * 资源文件模块
     */
    String FILE = "file";

    /**
     * 监控模块
     */
    String MONITOR = "monitor";

    /**
     * 应用模块
     */
    String APP = "app";

    /**
     * 基线告警模块
     */
    String ALARM = "alarm";

    /**
     * 模型仓库模块
     */
    String MODEL = "model";

    /**
     * Ai模块
     */
    String AI = "ai";

    /**
     * 对话模块
     */
    String CHAT = "chat";
}
