server:
  port: 30179

logging:
  exception-conversion-word: '%wEx'
  file:
    name: ./logs/zhishuyun-agent.log
  pattern:
    level: '%5p'
    dateformat: 'yyyy-MM-dd HH:mm:ss.SSS'
    console: '%clr(%d{${LOG_DATEFORMAT_PATTERN}}){faint} %clr(${LOG_LEVEL_PATTERN}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD}'
    file: '%d{${LOG_DATEFORMAT_PATTERN}} ${LOG_LEVEL_PATTERN} %-5(${PID:- }) --- [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD}}'
  register-shutdown-hook: false
  logback:
    rollingpolicy:
      clean-history-on-start: false
      file-name-pattern: '${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz'