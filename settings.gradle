rootProject.name = 'torch-yun'

include 'torch-yun-agent'
include 'torch-yun-dist'
include 'torch-yun-frontend'
include 'torch-yun-backend'
include 'torch-yun-backend:torch-yun-api'
include 'torch-yun-backend:torch-yun-api-base'
include 'torch-yun-backend:torch-yun-common'
include 'torch-yun-backend:torch-yun-modules'
include 'torch-yun-backend:torch-yun-main'
include 'torch-yun-backend:torch-yun-backend'
include 'torch-yun-backend:torch-yun-security'
include 'torch-yun-plugins'
include 'torch-yun-vip'
include 'torch-yun-vip:torch-yun-backend'
include 'torch-yun-vip:torch-yun-license'
