{"home": "Home", "zhi_qing_yun": "ZhiHui Cloud", "enterprise_data_platform": "Lightweight Large Model Training Platform", "quick_start": "Quick Start", "experience_now": "Experience Now", "video_introduction": "Video Introduction", "copy_success": "Copy Successful", "choose_light_cloud": "<PERSON><PERSON>", "light_cloud_description": "ZhiHui Cloud is an enterprise-level large model training platform. It features one-click Docker deployment and is ready to use out of the box. It enables quick enterprise-level large model training, tuning, and inference, compatible with QianYiTongWen and DeepSeek. The project is continuously updated and the source code is permanently open-source.", "light_cloud_description_mobile": "ZhiHui Cloud is an enterprise-level large model training platform. It features one-click Docker deployment and is ready to use out of the box. It enables quick enterprise-level large model training, tuning, and inference, compatible with QianYiTongWen and DeepSeek. The project is continuously updated and the source code is permanently open-source.", "related_technologies": "Related Technologies", "coding_capability": "Local Large Models, More Secure", "job_types_supported": "By deploying large models locally, enterprises can ensure data security and privacy protection, effectively reducing external risks. This approach provides businesses with more reliable solutions and strong safeguards, facilitating a smooth digital transformation.", "job_orchestration": "Programmable Code, Easier Training", "job_support": "The system offers a simple and easy-to-use programming interface, enabling developers to quickly write, debug, and deploy code, significantly lowering the technical barrier. This not only enhances development efficiency but also accelerates innovation, allowing ideas to be realized faster.", "real_work": "Open Interfaces, Faster Integration", "real_work_description": "Open API interfaces allow seamless integration between different systems and services, promoting data sharing and integration. This flexibility speeds up the optimization of business processes, helping enterprises respond more quickly to market changes.", "data_drives_value": "Embrace the New Era of AI", "data_drives_value_description": "AI has arrived, and ZhiHui Cloud is here to partner with you to realize the implementation of enterprise-level AI platforms.", "multi_platform_deployment": "Task Orchestration, More Efficient Business", "multi_platform_description": "The intelligent task orchestration feature effectively optimizes business processes, increases operational efficiency, and reduces resource waste. By automating task management, businesses can achieve more efficient operations and focus on enhancing and innovating core business areas.", "data_view": "Customizable, More Professional Robots", "data_view_description": "The system supports highly customizable configurations, allowing adjustments based on the specific needs of different enterprises. This way, robotic solutions can be more specialized, better adapting to specific industries and application scenarios, providing users with higher quality services.", "opensource_value": "Open Source Empowers Enterprises to Access AI", "free_trial": "Free Trial", "zhi_yao_shu_ju": "ZhiYao Data", "build_enterprise_open_source_software": "Lightweight Large Model Training Platform"}