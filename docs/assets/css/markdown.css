/* @import 'wbm/fonts.css'; */

/* :root {
    --side-bar-bg-color: #fff;
    --control-text-color: #777;
    --font-sans-serif: 'Ubuntu', 'Source Sans Pro', sans-serif !important;
    --font-monospace: 'Fira Code', 'Roboto Mono', monospace !important;
} */

:root {
    --side-bar-bg-color: #fff;
    --control-text-color: #777;
    --select-text-bg-color: #ffafa3;
    --active-file-text-color: #262626;
    --active-file-border-color: #f22f27;
    /* --active-file-bg-color: #fff3f0; */
    --primary-color: #f22f27;
    /* 中性色 */
    --mid-1: #ffffff;
    --mid-2: #fafafa;
    --mid-3: #f5f5f5;
    --mid-4: #f0f0f0;
    --mid-5: #d9d9d9;
    --mid-6: #bfbfbf;
    --mid-7: #8c8c8c;
    --mid-8: #595959;
    --mid-9: #434343;
    --mid-10: #262626;
    --mid-11: #1f1f1f;
    --mid-12: #141414;
    --mid-13: #000000;
    /* 主题色 */
    --main-1: #fff3f0;
    --main-2: #ffd4cc;
    --main-3: #ffafa3;
    --main-4: #ff887a;
    --main-5: #ff5d52;
    --main-6: #f22f27;
    --main-7: #cc1616;
    --main-8: #a60a0f;
    --main-9: #80010a;
    --main-10: #590009;
}

/* html {
    font-size: 17px;
} */

body {
    /* font-size: 17px; */
    /* font-family: Optima-Regular, Optima, PingFangSC-light, PingFangTC-light, "PingFang SC", Cambria, Cochin, Georgia, Times, "Times New Roman", serif; */
    /* font-family: 'Source Han SerifCN', Georgia, Times, 'SimSun', serif!important; */
    /* color: var(--mid-13);
    -webkit-font-smoothing: antialiased;
    line-height: 1.8rem;
    letter-spacing: 0;
    margin: 0;
    overflow-x: hidden; */
}

#write {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 30px 100px;
}

#write p {
    line-height: 1.8rem;
    word-spacing: .05rem;
    /* color: #3f3f3f; */
    color: var(--mid-13);
}


/* #write ol li {
    padding-left: 0.5rem;
} */

#write>ul:first-child,
#write>ol:first-child {
    margin-top: 30px;
}

body>*:first-child {
    margin-top: 0 !important;
}

body>*:last-child {
    margin-bottom: 0 !important;
}


/* a {
    color: var(--main-6);
    font-weight: 500;
    padding: 0 2px;
    text-decoration: none;
} */

a {
    color: blue;
    font-weight: 500;
    padding: 0 2px;
    text-decoration: none;
}


/* 链接 */


/* #write a {
    border-bottom: 1px solid var(--main-6);
    color: var(--main-6);
    text-decoration: none;
} */

#write a {
    border-bottom: 1px solid blue;
    color: blue;
    text-decoration: none;
}


/* 目录 */

#write a.md-toc-inner {
    line-height: 1.6;
    white-space: pre-line;
    border-bottom: none;
}

#write a:hover {
    border-bottom: 2px solid var(--main-6);
    color: var(--main-7);
}

h1,
h2,
h3,
h4,
h5,
h6 {
    position: relative;
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-weight: bold;
    line-height: 1.4;
    cursor: text;
}

h1:hover a.anchor,
h2:hover a.anchor,
h3:hover a.anchor,
h4:hover a.anchor,
h5:hover a.anchor,
h6:hover a.anchor {
    text-decoration: none;
}

h1 tt,
h1 code {
    font-size: inherit !important;
}

h2 tt,
h2 code {
    font-size: inherit !important;
}

h3 tt,
h3 code {
    font-size: inherit !important;
}

h4 tt,
h4 code {
    font-size: inherit !important;
}

h5 tt,
h5 code {
    font-size: inherit !important;
}

h6 tt,
h6 code {
    font-size: inherit !important;
}

h2 a,
h3 a {
    color: #34495e;
}

h1 {
    padding-bottom: 0.3em;
    font-size: 2.2em;
    line-height: 1.2;
    margin: 2.4em auto 1.2em;
    color: var(--main-10);
}

h1:after {
    content: '';
    display: block;
    margin: 0.2em auto 0;
    width: 1200px;
    height: 2px;
    border-bottom: 2px solid var(--main-6);
}

h2 {
    margin: 2em auto 1.4em;
    /* padding-left: 10px; */
    /* display:inline-block; */
    line-height: 1.4;
    font-size: 1.8em;
    /* border-left: 9px solid var(--main-6); */
    /* border-bottom: 1px solid #ddd; */
    border-bottom: 1px solid var(--main-6);
}

h2::before {
    content: '# '!important;
    color: #f22f27;
}

h3 {
    font-size: 1.4em;
    line-height: 1.43;
    margin: 1.6em auto 1.2em;
    padding-left: 9px;
    border-left: 5px solid #f22f27;
}


/* 三级四级标题点击后左边的提示图标 */

#write>h3.md-focus:before,
#write>h4.md-focus:before {
    width: auto;
    height: auto;
    background-color: var(--main-5);
    color: var(--mid-1);
}

h4 {
    margin-top: 1.3em;
    font-size: 1.2em;
    padding-left: 6px;
    padding-right: 6px;
    display: inline-block;
    border: 1px solid var(--main-6);
    border-top: 4px solid var(--main-6);
}

#write h5::before,
#write h6::before {
    position: absolute;
    right: calc(100% + .75em);
    top: 0;
    color: #5b5b5b;
    font-size: 0.8rem;
    font-weight: bold;
    font-variant: 'small-caps';
    white-space: nowrap;
    /* 文本强制不换行 */
    border: 0;
}

#write h5 {
    /* margin-left: 2em; */
    font-size: 1rem;
}

#write h6 {
    /* margin-left: 2.5em; */
    font-size: 1rem;
}

#write h5::before {
    content: 'H5';
    top: 0.18rem;
}

#write h6::before {
    content: 'H6';
    top: 0.18rem;
}

p,
blockquote,
ul,
ol,
dl,
table {
    margin: 0.8em 0;
}

li>ol,
li>ul {
    margin: 0 0;
}

hr {
    height: 2px;
    padding: 0;
    margin: 16px 0;
    background-color: #e7e7e7;
    border: 0 none;
    overflow: hidden;
    box-sizing: content-box;
}

body>h2:first-child {
    margin-top: 0;
    padding-top: 0;
}

body>h1:first-child {
    margin-top: 0;
    padding-top: 0;
}

body>h1:first-child+h2 {
    margin-top: 0;
    padding-top: 0;
}

body>h3:first-child,
body>h4:first-child,
body>h5:first-child,
body>h6:first-child {
    margin-top: 0;
    padding-top: 0;
}

a:first-child h1,
a:first-child h2,
a:first-child h3,
a:first-child h4,
a:first-child h5,
a:first-child h6 {
    margin-top: 0;
    padding-top: 0;
}

h1 p,
h2 p,
h3 p,
h4 p,
h5 p,
h6 p {
    margin-top: 0;
}

#write ol,
#write ul {
    padding-left: 25px;
    margin: .5rem 0;
}

#write ol>li,
#write ul>li {
    color: #db4d52;
    font-weight: bold;
}

#write ol>li>*,
#write ul>li>* {
    color: #333;
    font-weight: normal;
}

#write ol>li>*:not(ol):not(ul),
#write ul>li>*:not(ol):not(ul) {
    padding-left: .25rem;
}

#write ul {
    list-style-type: disc;
}

blockquote {
    border-left: 4px solid rgb(239, 112, 96);
    padding: 10px 15px;
    color: #3f3f3f;
    background-color: #fff9f9;
}

table {
    padding: 0;
    word-break: initial;
}

table tr {
    border-top: 1px solid var(--main-6);
    margin: 0;
    padding: 0;
}

table tr:nth-child(2n),
thead {
    background-color: #fafafa;
}

table tr th {
    font-weight: bold;
    border: 1px solid var(--main-6);
    border-bottom: 0;
    text-align: left;
    margin: 0;
    padding: 6px 13px;
}

table tr td {
    border: 1px solid var(--main-6);
    text-align: left;
    margin: 0;
    padding: 6px 13px;
}

table tr th:first-child,
table tr td:first-child {
    margin-top: 0;
}

table tr th:last-child,
table tr td:last-child {
    margin-bottom: 0;
}

#write strong {
    font-weight: bold;
    color: rgb(255, 53, 2);
}

#write em {
    padding: 0 2px 0 2px;
    font-style: normal;
    color: #ff3502;
    /* color: #595959;
    background: #F6EEFF; */
}


/* 数学公式变蓝 */

[md-inline='inline_math'] {
    color: blue;
    font-size: 100%;
}


/* 表格第一行 */


/* #write table thead th {
    background-color: var(--main-7);
    color: #f8f8f8;
} */


/* 行号左框线 */

#write .CodeMirror-gutters {
    border-right: 1px solid rgba(204, 51, 0);
}


/* 代码框 */

#write .md-fences {
    border: 1px solid #7a7a7a;
    -webkit-font-smoothing: initial;
    margin: 2rem 0 !important;
    /* padding: 0.3rem 0 !important; */
    padding: 3px 5px;
    line-height: 1.55rem;
    border-radius: 10px;
    font-family: 'Roboto Mono', 'Source Sans Pro', 'Microsoft YaHei', '微软雅黑' !important;
    font-size: 0.9rem;
    word-wrap: normal;
}

#write [mdtype="math_block"] {
    font-size: 1.2rem;
}

#write .CodeMirror-wrap .CodeMirror-code pre {
    padding-left: 12px;
    line-height: 1.55rem;
}

.cm-s-inner .CodeMirror-linenumber {
    width: 2ch !important;
    color: rgba(128, 128, 255, 0.8);
}

#write .CodeMirror-cursors .CodeMirror-cursor {
    border-left: 2px solid var(--main-4);
}


/* 行间代码 */

/*code block*/
#write .md-fences {
    font-size: 1rem;
    padding: 0.5rem !important;
    border-radius: 5px;
    font-family: inherit !important;
    word-wrap: normal;
    background-color: #2b2b2b;
    color: #A9B7C6;
    border: none;
    margin-left: 2px;
    margin-right: 2px;
}

/*code snippet*/

#write code, tt {
    overflow-wrap: break-word;
    padding: 2px 4px;
    border-radius: 4px;
    margin: 0px 2px;
    color: #e22749;
    background: rgb(248, 248, 248);
}

/* heighlight. */
#write mark {
    background-color: #e7ecf3;
    color: inherit;
    border-radius: 2px;
    padding: 2px 2px;
    margin: 0 2px;
}

#write del {
    padding: 1px 2px;
}

.cm-s-inner .cm-link,
.cm-s-inner.cm-link {
    color: #22a2c9;
}

.cm-s-inner .cm-string {
    color: #22a2c9;
}

.md-task-list-item > input {
    margin-left: -1.3em;
}

@media print {
    html {
        font-size: 12px;
    }

    table,
    pre {
        page-break-inside: avoid;
    }

    pre {
        word-wrap: break-word;
    }
}

#write pre.md-meta-block {
    padding: 1rem;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f7f7f7;
    border: 0;
    border-radius: 2px;
    color: #777777;
    margin-top: 0 !important;
}

.mathjax-block > .code-tooltip {
    bottom: .375rem;
}

#write > h3.md-focus:before {
    left: -1.5625rem;
    top: .375rem;
}

#write > h4.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

#write > h5.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

#write > h6.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

.md-image > .md-meta {
    border-radius: 2px;
    font-family: initial;
    padding: 2px 0 0 4px;
    color: inherit;
}

.md-tag {
    color: inherit;
}

.md-toc {
    margin-top: 20px;
    padding-bottom: 20px;
}

.sidebar-tabs {
    border-bottom: none;
}

#typora-quick-open {
    border: 1px solid #ddd;
    background-color: #f8f8f8;
}

.typora-quick-open-item {
    font-size: 1rem !important;
    height: 48px; 
    padding-left: 8px;
    padding-top: 4px;
}

#md-notification:before {
    top: 10px;
}

/* 流程图块 */

#write .md-diagram-panel {
    position: relative;
    margin: 24px auto;
}

#write .md-focus .md-diagram-panel {
    border: 1px solid var(--main-4);
    border-radius: 4px;
}



/* heighlight. */
#write mark {
    background-color: #e7ecf3;
    color: inherit;
    border-radius: 2px;
    padding: 2px 2px;
    margin: 0 2px;
}

#write del {
    padding: 1px 2px;
}

.cm-s-inner .cm-link,
.cm-s-inner.cm-link {
    color: #22a2c9;
}

.cm-s-inner .cm-string {
    color: #22a2c9;
}

.md-task-list-item > input {
    margin-left: -1.3em;
}

@media print {
    html {
        font-size: 12px;
    }

    table,
    pre {
        page-break-inside: avoid;
    }

    pre {
        word-wrap: break-word;
    }
}

#write pre.md-meta-block {
    padding: 1rem;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f7f7f7;
    border: 0;
    border-radius: 2px;
    color: #777777;
    margin-top: 0 !important;
}

.mathjax-block > .code-tooltip {
    bottom: .375rem;
}

#write > h3.md-focus:before {
    left: -1.5625rem;
    top: .375rem;
}

#write > h4.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

#write > h5.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

#write > h6.md-focus:before {
    left: -1.5625rem;
    top: .285714286rem;
}

.md-image > .md-meta {
    border-radius: 2px;
    font-family: initial;
    padding: 2px 0 0 4px;
    color: inherit;
}

.md-tag {
    color: inherit;
}

.md-toc {
    margin-top: 20px;
    padding-bottom: 20px;
}

.sidebar-tabs {
    border-bottom: none;
}

#typora-quick-open {
    border: 1px solid #ddd;
    background-color: #f8f8f8;
}

.typora-quick-open-item {
    font-size: 1rem !important;
    height: 48px; 
    padding-left: 8px;
    padding-top: 4px;
}

#md-notification:before {
    top: 10px;
}

/** focus mode */

.on-focus-mode blockquote {
    border-left-color: rgba(85, 85, 85, 0.12);
}

header,
.context-menu,
.megamenu-content,
footer {
    font-family: initial;
}

.file-node-content:hover .file-node-icon,
.file-node-content:hover .file-node-open-state {
    visibility: visible;
}

.mac-seamless-mode #typora-sidebar {
    background-color: #fafafa;
    font-size: 1rem;
    background-color: var(--side-bar-bg-color);
} 

.md-lang {
    color: #b4654d;
}

.html-for-mac .context-menu {
    --item-hover-bg-color: #E6F0FE;
}


/*insert element*/

span.cm-comment {
    color: #808080 !important;
}

.ty-quick-open-category.ty-has-prev .ty-quick-open-category-title {
    border-top:none;
}

.file-list-item-summary {
    font-family: initial;
}

#typora-quick-open-input {
    margin: 8px;
    box-shadow:none;
    border-radius:2px;
}

#typora-quick-open-input input {
    font-size: 1rem;
    box-shadow:none;
    padding-top: 2px;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 32px;
    max-height: 32px;
}

.modal-dialog#typora-quick-open{
    border-radius: 2px;
}

.ty-quick-open-category-title {
    padding-left: 8px;
    color: #BEBEBE;
}

.typora-quick-open-item-path {
    font-size: 10px;
    text-overflow:ellipsis;
    white-space:nowrap
}

.outline-item {
    font-size: 1rem;
}

.sidebar-tab {
    font-size: 1rem;
}

/*footnotes mark*/
#write .md-footnote {
    background-color: inherit;
    color: var(--drake-highlight);
    font-size: 0.9rem;
    border-radius: 0.9rem;
    padding-left: 0;
}

#write .md-footnote:before {
    content: "[";
}
#write .md-footnote:after {
    content: "]";
}

/*footnotes content*/
.md-hover-tip .code-tooltip-content {
    border-radius: 2px;
}

/*footnotes title*/
span.md-def-name {
    padding-right: 3ch;
    padding-left: 0;
    position:relative;
    font-weight: normal;
}

/*footnotes desc*/
.footnotes {
    font-size: 1rem;
    font-weight: normal;
    color: #304455;
    position:relative;
}

/*footnotes tooltip text*/
.code-tooltip-content .md-plain {
    font-size: 0.9rem;
    font-family: inherit;
}

.code-tooltip-content code {
    padding: 0 2px;
    font-family: inherit;
    color: #FFD760;
    background-color: inherit;
}

.code-tooltip-content a {
    color: #FFD760;
}

div.code-tooltip-content {
    box-shadow: 0 0 8px rgba(0,0,0,0.5);
}

.footnotes {
    opacity:1;
}

.md-def-name:after {
    content: ". ^";
    color: #304455;
}

.md-def-footnote .md-def-name:before {
    content: "";
    color: #304455;
    position:absolute;
}

.md-def-name:before {
    content: "";
    color: #304455;
    position:absolute;
}

.CodeMirror-scroll::-webkit-scrollbar {
    display: none;
}

.file-list-item-summary {
    font-size: 1em;
}

.pin-outline #outline-content .outline-active strong, .pin-outline .outline-active {
    font-weight: 500;
    color: var(--drake-highlight);
}

.file-list-item.active {
    border-left: 4px solid var(--drake-accent);
}

/**
    code render
    Name: IntelliJ IDEA darcula theme
    From IntelliJ IDEA by JetBrains
 */
/*.cm-s-inner  {  }*/
.cm-s-inner.CodeMirror { background: #2B2B2B; color: #A9B7C6; }

.cm-s-inner span.cm-meta { color: #BBB529; }
.cm-s-inner span.cm-number { color: #6897BB; }
.cm-s-inner span.cm-keyword { color: #CC7832; }
.cm-s-inner span.cm-def { color: #FFD760; }
.cm-s-inner span.cm-variable { color: #A9B7C6; }
.cm-s-inner span.cm-variable-2 { color: #A9B7C6; }
.cm-s-inner span.cm-variable-3 { color: #9876AA; }
.cm-s-inner span.cm-type { color: #AABBCC; }
.cm-s-inner span.cm-property { color: #FFC66D; }
.cm-s-inner span.cm-operator { color: #A9B7C6; }
.cm-s-inner span.cm-string { color: #6A8759; }
.cm-s-inner span.cm-string-2 { color: #6A8759; }
.cm-s-inner span.cm-comment { color: #61A151; }
.cm-s-inner span.cm-link { color: #CC7832; }
.cm-s-inner span.cm-atom { color: #CC7832; }
.cm-s-inner span.cm-error { color: #BC3F3C; }
.cm-s-inner span.cm-tag { color: #E8BF6A; }
.cm-s-inner span.cm-quote { color: #a6e22e; }
.cm-s-inner span.cm-attribute { color: #9876AA; }
.cm-s-inner span.cm-qualifier { color: #6A8759; }
.cm-s-inner span.cm-bracket { color: #E8BF6A; }
.cm-s-inner span.cm-builtin { color: #FF9E59; }
.cm-s-inner span.cm-special { color: #FF9E59; }
.cm-s-inner span.cm-matchhighlight { color: #FFFFFF; background-color: rgba(50, 89, 48, .7); font-weight: normal;}
.cm-s-inner span.cm-searching { color: #FFFFFF; background-color: rgba(61, 115, 59, .7); font-weight: normal;}

.cm-s-inner .CodeMirror-cursor { border-left: 1px solid #A9B7C6; }
.cm-s-inner .CodeMirror-gutters { background: #313335; border-right: 1px solid #313335; }
.cm-s-inner .CodeMirror-guttermarker { color: #FFEE80; }
.cm-s-inner .CodeMirror-guttermarker-subtle { color: #D0D0D0; }
.cm-s-inner .CodeMirrir-linenumber { color: #606366; }
.cm-s-inner .CodeMirror-matchingbracket { background-color: #3B514D; color: #FFEF28 !important; }

.cm-s-inner .CodeMirror-selected{ background: #214283 !important; }
.cm-s-inner .CodeMirror-selectedtext { background: #214283 !important; }
.cm-overlay.CodeMirror-selectedtext { background: #B5D6FC !important; }
.cm-s-inner div.CodeMirror-cursor { border-left: 1px solid #ffffff; }


/*--- 图片 ---*/

.md-image {
    margin: 24px auto;
    border-radius: 4px;
}

.md-image img {
    border-radius: 4px;
    border-top:1px solid #ccc;
    border-right:1px solid #666;
    border-bottom:2px solid #999;
    border-left:1px solid #ccc;
    padding: 7px;
}


/* 当 “![shadow-随便写]()”写时，会有阴影 */

.md-image img[alt|='shadow'] {
    /* box-shadow: 0 4px 24px -6px #ddd; */
    box-shadow: #84A1A8 0px 10px 15px;
}

.md-image>.md-meta {
    border-radius: 3px;
    font-family: Consolas, 'Liberation Mono', Courier, monospace;
    padding: 2px 0 0 4px;
    font-size: 0.9em;
    color: inherit;
}

.md-tag {
    color: inherit;
}

.md-toc {
    margin-top: 20px;
    padding-bottom: 20px;
}

.sidebar-tabs {
    border-bottom: none;
}

#typora-quick-open {
    border: 1px solid #ddd;
    background-color: #f8f8f8;
}

#typora-quick-open-item {
    background-color: #FAFAFA;
    border-color: #FEFEFE #e5e5e5 #e5e5e5 #eee;
    border-style: solid;
    border-width: 1px;
}

#md-notification:before {
    top: 10px;
}


/** focus mode */

.on-focus-mode blockquote {
    border-left-color: rgba(85, 85, 85, 0.12);
}

header,
.context-menu,
.megamenu-content,
footer {
    font-family: 'Segoe UI', 'Arial', sans-serif;
}

.file-node-content:hover .file-node-icon,
.file-node-content:hover .file-node-open-state {
    visibility: visible;
}

.mac-seamless-mode #typora-sidebar {
    background-color: var(--side-bar-bg-color);
}

.md-lang {
    color: #b4654d;
}

.html-for-mac .context-menu {
    --item-hover-bg-color: #e6f0fe;
}


/* 侧边栏 */

.file-list-item.active {
    background: var(--active-file-bg-color);
    color: var(--active-file-text-color);
    border-left: 4px solid var(--main-6);
}

.file-tree-node.active>.file-node-background {
    background-color: var(--active-file-bg-color);
    border-left: 4px solid var(--main-6);
    border-color: var(--active-file-border-color);
}

@media print {
    .typora-export * {
        -webkit-print-color-adjust: exact;
    }
    html {
        font-size: 16px!important;
    }
    body {
        /* font-family: 'Source Han SerifCN', Georgia, Times, 'SimSun', serif!important; */
        /* font-size: 16px!important; */
        font-family: Times, 'SimSun', serif!important;
        color: #000000!important;
    }
    p {
        color: #000000!important;
    }
    a {
        color: blue!important;
        /* border-bottom: 1px solid blue!important; */
    }
    table,
    pre {
        page-break-inside: avoid;
    }
    pre {
        word-wrap: break-word;
    }
}

