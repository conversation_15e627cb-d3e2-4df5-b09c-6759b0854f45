---
title: "访问地址说明"
---

#### 至慧云界面访问

> 访问至慧云平台 
> 密码配置项：`application-local.yml`中的`isx-app.admin-passwd`

- 访问地址：http://localhost:8080/ 
- 管理员账号：admin 
- 默认密码：admin123

![20241126205343](https://img.isxcode.com/picgo/20241126205343.png)

#### 默认H2数据库访问

> 注意！！！请使用无痕模式访问 
> 密码配置项：`application-local.yml`中的`spring.security.user.password`

- 地址：http://localhost:8080/h2-console 
- 账号：admin 
- 密码：admin123 

![20241126205454](https://img.isxcode.com/picgo/20241126205454.png)

- url: jdbc:h2:file:~/.zhihuiyun/h2/data;AUTO_SERVER=TRUE 
- username: root 
- password: root123

![20241126205558](https://img.isxcode.com/picgo/20241126205558.png)

![20241126205618](https://img.isxcode.com/picgo/20241126205618.png)


#### Druid界面访问

> 注意！！！请使用无痕模式访问 
> 密码配置项：`application-local.yml`中的`spring.security.user.password`

- 地址：http://localhost:8080/druid/index.html 
- 账号：admin 
- 密码：admin123 

![20241126205743](https://img.isxcode.com/picgo/20241126205743.png)

#### Swagger接口文档访问

> 注意！！！请使用无痕模式访问 
> 密码配置项：`application-local.yml`中的`spring.security.user.password`

- 地址：http://localhost:8080/swagger-ui/index.html 
- 账号：admin 
- 密码：admin123 

![20241126205825](https://img.isxcode.com/picgo/20241126205825.png)