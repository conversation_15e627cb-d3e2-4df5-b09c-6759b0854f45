---
title: "租户列表"
---

## 租户模块

> 通过`TenantId`字段对租户之间的数据进行隔离

#### 查询租户

> `后台管理员`账号登录   
> 点击`租户列表`菜单，查询平台的租户，支持租户名搜索

![20241220115551](https://img.isxcode.com/picgo/20241220115551.png)

#### 新建租户

> 点击`添加租户`按钮，新建租户   
> 不同租户之间数据独立，无法互相访问   
> 注意！！！最大可创建的租户数量，受限于许可证中的租户数

![20241220115723](https://img.isxcode.com/picgo/20241220115723.png)

- **租户名称**: 必填，系统中显示的租户名称
- **成员数**: 必填，默认为5，小于1无法添加成员，最大值受限于许可证中成员数
- **作业流数**: 必填，默认为20，小于1无法新建作业流，最大值受限于许可证中作业流数
- **管理员**: 必填，指定用户为`租户管理员`，`租户管理员`拥有`添加租户成员`、`授权管理员`、`资源删除`等权限
- **备注**: 非必填

#### 检测租户

> 点击`检测`按钮，可以刷新当前租户已使用的成员名额和作业流数量占比

![20241220135801](https://img.isxcode.com/picgo/20241220135801.png)

#### 租户切换

> 当用户所属多个租户时，可通过`点击头像`切换租户

![20241213180924](https://img.isxcode.com/picgo/20241213180924.png)

#### 租户禁用

> 被禁用的租户，将无法切换且无法登录平台

![20241219162354](https://img.isxcode.com/picgo/20241219162354.png)