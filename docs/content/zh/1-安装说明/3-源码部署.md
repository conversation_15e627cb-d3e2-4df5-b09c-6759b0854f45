---
title: "源码部署"
---

## 源码编译打包部署至慧云

#### 编译环境

- Java: zulu8.78.0.19-ca-jdk8.0.412-x64 
- Nodejs: node-v18.20.3-x64

#### 源码下载

```bash
git clone https://github.com/isxcode/torch-yun.git
```

#### 安装项目依赖

```bash
cd torch-yun
./gradlew install
```

#### 编译代码

```bash
cd torch-yun
./gradlew package
```

#### 打包路径

```bash
torch-yun/torch-yun-dist/build/distributions/zhihuiyun.tar.gz
```

#### 解压部署

```bash
tar -vzxf zhihuiyun.tar.gz
bash zhihuiyun/bin/start.sh
```

#### 访问项目

- 访问地址: http://localhost:8080 
- 管理员账号：`admin` 
- 管理员密码：`admin123`