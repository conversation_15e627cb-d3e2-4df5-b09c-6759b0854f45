---
title: "高可用部署"
---

## 基于Nginx实现至慧云高可用多活部署

#### 资源购买

> 配置：centos7.9、4核心、16GB内存

- **服务器1**: nfs共享盘服务 
- **共享磁盘**: 挂载到服务器1中 
- **服务器2**: nginx负载均衡服务 
- **服务器3**: 安装至慧云1 
- **服务器4**: 安装至慧云2 
- **服务器5**: 安装至慧云3

| 服务器名 | 公网ip          | 内网ip          |
|------|---------------|---------------|
| 服务器1 | ************  | ************* |
| 服务器2 | ************* | ************* |
| 服务器3 | ************  | ************* |
| 服务器4 | ***********   | ************* |
| 服务器5 | **********    | ************* |

#### 挂载磁盘

服务器1挂载磁盘，参考文档：[linux 挂载磁盘](https://ispong.isxcode.com/os/linux/linux%20%E6%8C%82%E8%BD%BD%E7%A3%81%E7%9B%98/)

#### 安装nfs服务

服务器1安装nfs服务，参考文档：[linux 安装nfs服务](https://ispong.isxcode.com/os/linux/linux%20%E5%AE%89%E8%A3%85nfs%E6%9C%8D%E5%8A%A1/)

#### 挂载共享盘

给服务器3、服务器4、服务器5挂载服务器的共享盘，参考文档：[linux 挂载共享盘](https://ispong.isxcode.com/os/linux/linux%20%E6%8C%82%E8%BD%BD%E5%85%B1%E4%BA%AB%E7%A3%81%E7%9B%98/)

#### 安装至慧云服务

给服务器3、服务器4、服务器5安装至慧云服务，参考文档：[至慧云离线部署](/docs/zh/1/2)

#### 修改配置文件，将资源目录指向共享盘

> 包含以下配置项：
> spring.datasource.url: 数据库数据目录  
> isx-app.resources-path: 资源文件目录

```bash
vim zhihuiyun/conf/application-local.yml
```

```yaml
server:
  port: 8080

spring:

  security:
    user:
      name: admin
      password: admin123

  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:file:/data/zhihuiyun/h2/data;AUTO_SERVER=TRUE
    username: root
    password: root1234

isx-app:
  resources-path: /data/zhihuiyun
  admin-passwd: admin123
  use-ssl: false
```

> 注意！！！复制数据库驱动文件到共享盘 
> 只需要执行一次,不需要重复执行

```bash
mkdir -p /data/zhihuiyun
cp -r zhihuiyun/resources/jdbc /data/zhihuiyun/
```

#### 启动项目

> 三台服务器都要启动至慧云服务

```bash
bash zhihuiyun/bin/start.sh
```

#### 安装nginx负载服务

给服务器2安装nginx负载服务
参考文档：[nginx 负载均衡配置](https://ispong.isxcode.com/vue/nginx/nginx%20%E8%B4%9F%E8%BD%BD%E5%9D%87%E8%A1%A1%E9%85%8D%E7%BD%AE/)

```wikitext
upstream zhihuiyun_servers {
    server *************:8080 weight=1 max_fails=3 fail_timeout=60s;
    server *************:8080 weight=1 max_fails=3 fail_timeout=60s;
    # 所有节点挂掉后使用
    server *************:8080 backup;
}
   
server {
    location / {
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_pass http://zhihuiyun_servers/;
    }
}
```

#### 通过nginx访问至慧云

- 网址：http://************* 
- 账号：admin 
- 密码：admin123