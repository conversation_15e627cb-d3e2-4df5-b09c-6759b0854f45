---
title: "开机自启"
---

## 基于CentOS7.9配置至慧云开机自启

#### 将脚本文件赋予可执行权限

```bash
chmod a+x /opt/zhihuiyun/bin/start.sh
chmod a+x /opt/zhihuiyun/bin/stop.sh
```

#### 配置自启文件

```bash
vim /usr/lib/systemd/system/zhihuiyun.service
```

> 配置启动脚本路径和pid文件路径和启动用户

```bash
[Unit]
Description=Zhiqingyun Service unit Configuration
After=network.target

[Service]
Type=forking

ExecStart=/opt/zhihuiyun/bin/start.sh --print-log="false"
ExecStop=/opt/zhihuiyun/bin/stop.sh
ExecReload=/opt/zhihuiyun/bin/start.sh --print-log="false"
PIDFile=/opt/zhihuiyun/zhihuiyun.pid
KillMode=none
Restart=always
User=root
Group=root

[Install]
WantedBy=multi-user.target
```

#### 重载服务

```bash
systemctl daemon-reload
```

#### 设置开机自启

```bash
systemctl enable zhihuiyun
```

#### 相关操作命令

```bash
# 启动至慧云
systemctl start zhihuiyun

# 查看至慧云状态
systemctl status zhihuiyun

# 停止至慧云
systemctl stop zhihuiyun

# 重启至慧云
systemctl restart zhihuiyun
```