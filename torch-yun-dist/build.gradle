// 构建lib文件夹
tasks.register('build_agent_lib', Copy) {

    dependsOn(":torch-yun-agent:bootJar")
    dependsOn(":torch-yun-backend:torch-yun-api:jar")

    def dependencies = configurations.runtimeClasspath.findAll {
        it.name.contains('fastjson2') ||
                it.name.contains('fastjson') ||
                it.name.contains('torch-yun-api') ||
                it.name.contains('log4j-api')
    }
    dependencies.each { dependency ->
        from dependency
        into 'build/zhishuyun_agent/lib'
    }

    from rootDir.getAbsolutePath() + '/torch-yun-backend/torch-yun-api/build/libs'
    from rootDir.getAbsolutePath() + '/torch-yun-agent/build/libs'
    from rootDir.getAbsolutePath() + '/resources/jdbc/system'
    from rootDir.getAbsolutePath() + '/resources/cdc'
    into 'build/zhishuyun-agent/lib'
}

// 构建plugins文件夹
tasks.register('build_agent_plugins', Copy) {

    from rootDir.getAbsolutePath() + '/torch-yun-plugins/'
    into 'build/zhishuyun-agent/plugins'
}

// 构建至慧云代理
tasks.register('build_agent', Tar) {

    mustRunAfter(":torch-yun-frontend:make")

    dependsOn('build_agent_lib')
    dependsOn('build_agent_plugins')

    compression = Compression.GZIP
    archiveFileName = 'zhishuyun-agent.tar.gz'

    from(rootDir.getAbsolutePath() + "/torch-yun-dist/flink-min") {
        into 'zhishuyun-agent/flink-min'
    }
    from('zhishuyun-agent/bin') {
        into 'zhishuyun-agent/bin'
    }
    from(rootDir.getAbsolutePath() + "/torch-yun-agent/src/main/resources/application.yml") {
        into 'zhishuyun-agent/conf'
    }
    from('zhishuyun-agent/logs') {
        into 'zhishuyun-agent/logs'
    }
    from('build/zhishuyun-agent/plugins') {
        into 'zhishuyun-agent/plugins'
    }
    from('zhishuyun-agent/works') {
        into 'zhishuyun-agent/works'
    }
    from('zhishuyun-agent/file') {
        into 'zhishuyun-agent/file'
    }
    from('zhishuyun-agent/ai') {
        into 'zhishuyun-agent/ai'
    }
    from('build/zhishuyun-agent/lib') {
        into 'zhishuyun-agent/lib'
    }
    from('../README.md') {
        into 'zhishuyun-agent/'
    }
}

// 构建至慧云
tasks.register('build_zhishuyun', Tar) {

    mustRunAfter(":torch-yun-backend:make")

    compression = Compression.GZIP
    archiveFileName = 'zhishuyun.tar.gz'

    from('zhishuyun/bin') {
        into 'zhishuyun/bin'
    }
    from('zhishuyun/conf') {
        into 'zhishuyun/conf'
    }
    from(rootDir.getAbsolutePath() + '/torch-yun-backend/torch-yun-main/src/main/resources/application-local.yml') {
        into 'zhishuyun/conf'
    }
    from(rootDir.getAbsolutePath() + '/resources/jdbc/system') {
        into 'zhishuyun/resources/jdbc/system'
    }
    from(rootDir.getAbsolutePath() + '/torch-yun-backend/torch-yun-main/build/libs/zhishuyun.jar') {
        into 'zhishuyun/lib'
    }
    from(rootDir.getAbsolutePath() + '/README.md') {
        into 'zhishuyun/'
    }
}

// 打包
tasks.register('make', GradleBuild) {

    tasks = [":torch-yun-frontend:make", "build_agent", ":torch-yun-backend:make", "build_zhishuyun"]
}

// 添加依赖
dependencies {

}