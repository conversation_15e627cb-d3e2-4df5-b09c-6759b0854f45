{"name": "torch-yun-website", "version": "0.0.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite --mode dev", "serve-prod": "vite", "build": "run-p build-only", "build-bak": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "build-prod": "vite build --mode prod", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint-dev": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/"}, "dependencies": {"@antv/hierarchy": "^0.6.11", "@antv/layout": "^0.3.17", "@antv/x6": "^1.29.5", "@antv/x6-vue-shape": "^1.3.0", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/lang-sql": "^6.5.4", "@element-plus/icons-vue": "2.1.0", "axios": "^1.4.0", "clipboard": "^2.0.11", "dagre": "^0.8.5", "dayjs": "^1.11.10", "echarts": "^5.4.3", "element-plus": "2.3.5", "highlight.js": "^11.11.1", "jsplumb": "^2.15.6", "lodash-es": "^4.17.21", "marked": "^15.0.7", "mitt": "^3.0.0", "normalize.css": "8.0.1", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^3.2.0", "qs": "^6.11.2", "vue": "^3.3.2", "vue-codemirror6": "^1.1.24", "vue-grid-layout": "3.0.0-beta1", "vue-router": "^4.2.0", "vuedraggable": "4.1.0", "vxe-table": "4.4.2-beta.0", "xe-utils": "3.5.10"}, "devDependencies": {"@playwright/test": "^1.33.0", "@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.1", "@types/jsdom": "^21.1.1", "@types/lodash-es": "^4.17.9", "@types/node": "^18.16.8", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.3.2", "@vue/tsconfig": "^0.4.0", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "jsdom": "^22.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "sass": "1.62.1", "typescript": "~5.0.4", "unplugin-vue-components": "^0.25.2", "vite": "^4.3.5", "vite-plugin-static-copy": "^0.15.0", "vitest": "^0.31.0", "vue-tsc": "^1.6.4"}}