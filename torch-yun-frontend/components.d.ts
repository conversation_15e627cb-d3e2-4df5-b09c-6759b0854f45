/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    BlockDrawer: typeof import('./src/components/block-drawer/index.vue')['default']
    BlockModal: typeof import('./src/components/block-modal/index.vue')['default']
    BlockTable: typeof import('./src/components/block-table/index.vue')['default']
    CodeMirror: typeof import('./src/components/code-mirror/index.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    EllipsisTooltip: typeof import('./src/components/ellipsis-tooltip/ellipsis-tooltip.ts')['default']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTag: typeof import('element-plus/es')['ElTag']
    EmptyPage: typeof import('./src/components/empty-page/index.vue')['default']
    Loading: typeof import('./src/components/loading/index.vue')['default']
    LogContainer: typeof import('./src/components/log-container/index.vue')['default']
    ParseModal: typeof import('./src/components/log-container/parse-modal/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ZqyLog: typeof import('./src/components/zqy-log/index.vue')['default']
    ZStatusTag: typeof import('./src/components/z-status-tag/index.vue')['default']
    ZSvgIcon: typeof import('./src/components/z-svg-icon/index.vue')['default']
  }
}
