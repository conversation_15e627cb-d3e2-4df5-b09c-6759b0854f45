@import "./element-custom.scss";
@import "./variable/index.scss";

body * {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: element-icons !important;
  background-color: #f7f7f7;
  overflow: hidden;
  margin: 0;
  line-height: 1.15;
  font-variant: normal;
  width: 100% !important;

  .layout-middle {
    overflow-y: auto;
  }
}

*:focus {
  outline: none;
}

.zqy-seach-table {
  .zqy-loading {
    height: calc(100vh - 186px);
  }
  .zqy-table-top {
    height: 60px;
    display: flex;
    padding-left: 20px;
    padding-right: 20px;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    .zqy-seach {
      .el-input {
        width: 330px;
      }
    }
  }
  .zqy-table {
    padding: 0 20px;
    .vxe-table--body-wrapper {
      max-height: calc(100vh - 232px);
    }
    .btn-group {
      display: flex;
      justify-content: space-around;
      align-items: center;
      span {
        cursor: pointer;
        color: $--app-unclick-color;
        &:hover {
          color: getCssVar('color', 'primary');;
        }
      }
    }
  }
}
