export default {
    editConfig: {
        name: '邮箱输入',
        icon: 'Document',
        code: 'FormInputEmail'
    },
    componentConfig: {
        uuid: '16 uuid',
        type: 'simple',
        formValueCode: '',
        codeType: 'custom',
        label: '邮箱输入',
        placeholder: '请输入',
        disabled: false,
        required: false,
        isColumn: true,
        width: 2,
        componentType: 'FormInputEmail',
        valid: true
    },
    conponentSetConfig: [
        'LABEL',
        'CODE_SELECT',
        'PRIMARY_COLUMN',
        'WIDTH',
        'MAXLENGTH',
        'DEFAULTVALUE',
        'PLACEHOLDER',
        'DISABLED',
        'REQUIRED',
        'LIST_COLUMN'
    ]
}