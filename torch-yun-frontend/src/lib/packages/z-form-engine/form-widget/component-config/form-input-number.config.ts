export default {
    editConfig: {
        name: '数字输入',
        icon: 'Document',
        code: 'FormInputNumber'
    },
    componentConfig: {
        uuid: '16 uuid',
        type: 'simple',
        formValueCode: '',
        codeType: 'custom',
        label: '数字输入',
        placeholder: '请输入',
        disabled: false,
        required: false,
        isColumn: true,
        precision: null,
        width: 2,
        componentType: 'FormInputNumber',
        valid: true
    },
    conponentSetConfig: [
        'LABEL',
        'CODE_SELECT',
        'PRIMARY_COLUMN',
        'WIDTH',
        'PRECISION',
        'DEFAULTVALUE_NUMBER',
        'PLACEHOLDER',
        'DISABLED',
        'REQUIRED',
        'LIST_COLUMN'
    ]
}